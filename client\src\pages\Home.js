import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated, user } = useAuth();

  return (
    <div className="container">
      <div style={{ textAlign: 'center', padding: '4rem 0' }}>
        <h1 style={{ fontSize: '3rem', marginBottom: '1rem', color: '#2c3e50' }}>
          Welcome to MERN Hackathon App
        </h1>
        <p style={{ fontSize: '1.2rem', marginBottom: '2rem', color: '#7f8c8d' }}>
          A full-stack boilerplate ready for your next hackathon project
        </p>
        
        {isAuthenticated ? (
          <div>
            <h2 style={{ marginBottom: '1rem' }}>
              Hello, {user?.name}! 👋
            </h2>
            <Link to="/dashboard" className="btn btn-primary" style={{ marginRight: '1rem' }}>
              Go to Dashboard
            </Link>
            <Link to="/profile" className="btn btn-success">
              View Profile
            </Link>
          </div>
        ) : (
          <div>
            <Link to="/register" className="btn btn-primary" style={{ marginRight: '1rem' }}>
              Get Started
            </Link>
            <Link to="/login" className="btn btn-success">
              Login
            </Link>
          </div>
        )}

        <div style={{ marginTop: '4rem', textAlign: 'left', maxWidth: '800px', margin: '4rem auto 0' }}>
          <h2 style={{ marginBottom: '2rem', textAlign: 'center' }}>Features Included</h2>
          <div className="grid grid-2">
            <div className="card">
              <div className="card-header">🔐 Authentication</div>
              <div className="card-body">
                <p>JWT-based authentication with login, register, and protected routes</p>
              </div>
            </div>
            <div className="card">
              <div className="card-header">📊 CRUD Operations</div>
              <div className="card-body">
                <p>Complete Create, Read, Update, Delete functionality for items</p>
              </div>
            </div>
            <div className="card">
              <div className="card-header">🎨 Modern UI</div>
              <div className="card-body">
                <p>Clean, responsive design with React Router and context management</p>
              </div>
            </div>
            <div className="card">
              <div className="card-header">🚀 Ready to Deploy</div>
              <div className="card-body">
                <p>Production-ready setup with environment configuration</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
