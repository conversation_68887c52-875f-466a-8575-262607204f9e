{"name": "mern-server", "version": "1.0.0", "description": "Backend server for MERN hackathon app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}