import React from 'react';

const ItemCard = ({ item, onEdit, onDelete }) => {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#e74c3c';
      case 'medium': return '#f39c12';
      case 'low': return '#27ae60';
      default: return '#95a5a6';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return '#27ae60';
      case 'in-progress': return '#3498db';
      case 'pending': return '#f39c12';
      default: return '#95a5a6';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="card">
      <div className="card-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3 style={{ margin: 0, fontSize: '1.2rem' }}>{item.title}</h3>
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <span 
            style={{ 
              padding: '0.25rem 0.5rem', 
              borderRadius: '4px', 
              fontSize: '0.8rem',
              backgroundColor: getPriorityColor(item.priority),
              color: 'white'
            }}
          >
            {item.priority}
          </span>
          <span 
            style={{ 
              padding: '0.25rem 0.5rem', 
              borderRadius: '4px', 
              fontSize: '0.8rem',
              backgroundColor: getStatusColor(item.status),
              color: 'white'
            }}
          >
            {item.status}
          </span>
        </div>
      </div>
      
      <div className="card-body">
        <p style={{ marginBottom: '1rem' }}>{item.description}</p>
        
        <div style={{ marginBottom: '1rem' }}>
          <strong>Category:</strong> 
          <span style={{ 
            marginLeft: '0.5rem',
            padding: '0.25rem 0.5rem',
            backgroundColor: '#ecf0f1',
            borderRadius: '4px',
            fontSize: '0.9rem'
          }}>
            {item.category}
          </span>
        </div>

        {item.tags && item.tags.length > 0 && (
          <div style={{ marginBottom: '1rem' }}>
            <strong>Tags:</strong>
            <div style={{ marginTop: '0.5rem' }}>
              {item.tags.map((tag, index) => (
                <span 
                  key={index}
                  style={{ 
                    display: 'inline-block',
                    margin: '0.25rem 0.25rem 0.25rem 0',
                    padding: '0.25rem 0.5rem',
                    backgroundColor: '#3498db',
                    color: 'white',
                    borderRadius: '12px',
                    fontSize: '0.8rem'
                  }}
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        <div style={{ fontSize: '0.9rem', color: '#7f8c8d' }}>
          <p><strong>Created:</strong> {formatDate(item.createdAt)}</p>
          {item.updatedAt !== item.createdAt && (
            <p><strong>Updated:</strong> {formatDate(item.updatedAt)}</p>
          )}
        </div>
      </div>

      <div className="card-footer">
        <button 
          onClick={() => onEdit(item)}
          className="btn btn-primary"
          style={{ marginRight: '0.5rem' }}
        >
          Edit
        </button>
        <button 
          onClick={() => onDelete(item._id)}
          className="btn btn-danger"
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export default ItemCard;
