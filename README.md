# MERN Stack Hackathon Boilerplate

A complete, production-ready MERN (MongoDB, Express.js, React, Node.js) stack application boilerplate designed for hackathons and rapid prototyping.

## 🚀 Features

- **Authentication System**: JWT-based authentication with login/register
- **CRUD Operations**: Complete Create, Read, Update, Delete functionality
- **Modern UI**: Clean, responsive design with React Router
- **State Management**: React Context API for global state
- **API Integration**: Axios for HTTP requests with interceptors
- **Form Validation**: Client and server-side validation
- **Error Handling**: Comprehensive error handling and user feedback
- **Security**: Password hashing, JWT tokens, CORS protection
- **Development Ready**: Hot reload, environment variables, and more

## 📁 Project Structure

```
mern-hackathon-boilerplate/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── context/        # React Context providers
│   │   ├── pages/          # Page components
│   │   ├── utils/          # Utility functions
│   │   └── ...
│   └── package.json
├── server/                 # Express backend
│   ├── config/             # Database configuration
│   ├── middleware/         # Custom middleware
│   ├── models/             # Mongoose models
│   ├── routes/             # API routes
│   ├── .env.example        # Environment variables template
│   └── package.json
├── package.json            # Root package.json with scripts
└── README.md
```

## 🛠️ Quick Start

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd mern-hackathon-boilerplate
   ```

2. **Install dependencies**
   ```bash
   npm run install-all
   ```

3. **Set up environment variables**
   ```bash
   cd server
   cp .env.example .env
   ```
   
   Edit the `.env` file with your configuration:
   ```env
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/mern_hackathon
   JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
   NODE_ENV=development
   ```

4. **Start the development servers**
   ```bash
   npm run dev
   ```

   This will start both the backend server (port 5000) and React development server (port 3000).

## 📚 Available Scripts

### Root Directory
- `npm run dev` - Start both frontend and backend in development mode
- `npm run install-all` - Install dependencies for both client and server
- `npm run build` - Build the React app for production
- `npm start` - Start the production server

### Server Directory
- `npm start` - Start the production server
- `npm run dev` - Start the development server with nodemon

### Client Directory
- `npm start` - Start the React development server
- `npm run build` - Build the React app for production
- `npm test` - Run tests

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user (protected)

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID (protected)
- `PUT /api/users/:id` - Update user (protected)
- `DELETE /api/users/:id` - Delete user (admin only)

### Items
- `GET /api/items` - Get user's items (protected)
- `GET /api/items/:id` - Get single item (protected)
- `POST /api/items` - Create new item (protected)
- `PUT /api/items/:id` - Update item (protected)
- `DELETE /api/items/:id` - Delete item (protected)

## 🎨 Frontend Pages

- **Home** (`/`) - Landing page with feature overview
- **Login** (`/login`) - User authentication
- **Register** (`/register`) - User registration
- **Dashboard** (`/dashboard`) - Main app interface with CRUD operations
- **Profile** (`/profile`) - User profile management

## 🔐 Authentication Flow

1. User registers/logs in
2. Server returns JWT token
3. Token stored in localStorage
4. Token sent with API requests via Authorization header
5. Protected routes check for valid token

## 🗄️ Database Models

### User Model
```javascript
{
  name: String (required),
  email: String (required, unique),
  password: String (required, hashed),
  role: String (enum: ['user', 'admin']),
  createdAt: Date,
  updatedAt: Date
}
```

### Item Model
```javascript
{
  title: String (required),
  description: String (required),
  category: String (enum),
  priority: String (enum: ['low', 'medium', 'high']),
  status: String (enum: ['pending', 'in-progress', 'completed']),
  user: ObjectId (ref: 'User'),
  tags: [String],
  createdAt: Date,
  updatedAt: Date
}
```

## 🚀 Deployment

### Backend Deployment (Heroku example)
1. Create a Heroku app
2. Set environment variables in Heroku dashboard
3. Deploy using Git or GitHub integration

### Frontend Deployment (Netlify/Vercel example)
1. Build the React app: `cd client && npm run build`
2. Deploy the `build` folder to your hosting service
3. Set up environment variables if needed

## 🔧 Customization

### Adding New Features
1. **Backend**: Add new models in `server/models/`, routes in `server/routes/`
2. **Frontend**: Add new pages in `client/src/pages/`, components in `client/src/components/`

### Styling
- Modify `client/src/index.css` for global styles
- Add component-specific styles as needed

### Database
- Update models in `server/models/`
- Add new API endpoints in `server/routes/`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running locally or check your Atlas connection string
   - Verify the MONGODB_URI in your .env file

2. **Port Already in Use**
   - Change the PORT in your .env file
   - Kill any processes using the ports (3000, 5000)

3. **JWT Token Issues**
   - Ensure JWT_SECRET is set in your .env file
   - Clear localStorage if experiencing auth issues

4. **CORS Issues**
   - Verify the proxy setting in client/package.json
   - Check CORS configuration in server.js

## 🎯 Perfect for Hackathons

This boilerplate includes everything you need to quickly build and deploy a full-stack web application:

- ✅ User authentication and authorization
- ✅ Database integration with MongoDB
- ✅ RESTful API with Express.js
- ✅ Modern React frontend with routing
- ✅ Responsive design
- ✅ Error handling and validation
- ✅ Development and production configurations
- ✅ Easy deployment setup

Focus on building your unique features instead of setting up the basics!

---

**Happy Hacking! 🚀**
