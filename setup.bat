@echo off
echo ========================================
echo MERN Stack Hackathon Boilerplate Setup
echo ========================================
echo.

echo Installing root dependencies...
call npm install
echo.

echo Installing server dependencies...
cd server
call npm install
cd ..
echo.

echo Installing client dependencies...
cd client
call npm install
cd ..
echo.

echo Setting up environment file...
cd server
if not exist .env (
    copy .env.example .env
    echo Environment file created at server/.env
    echo Please edit server/.env with your configuration
) else (
    echo Environment file already exists
)
cd ..
echo.

echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit server/.env with your MongoDB URI and JWT secret
echo 2. Make sure MongoDB is running
echo 3. Run 'npm run dev' to start both servers
echo.
echo Happy hacking! 🚀
pause
