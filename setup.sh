#!/bin/bash

echo "========================================"
echo "MERN Stack Hackathon Boilerplate Setup"
echo "========================================"
echo

echo "Installing root dependencies..."
npm install
echo

echo "Installing server dependencies..."
cd server
npm install
cd ..
echo

echo "Installing client dependencies..."
cd client
npm install
cd ..
echo

echo "Setting up environment file..."
cd server
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Environment file created at server/.env"
    echo "Please edit server/.env with your configuration"
else
    echo "Environment file already exists"
fi
cd ..
echo

echo "========================================"
echo "Setup Complete!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Edit server/.env with your MongoDB URI and JWT secret"
echo "2. Make sure MongoDB is running"
echo "3. Run 'npm run dev' to start both servers"
echo
echo "Happy hacking! 🚀"
