import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '../utils/api';
import ItemForm from '../components/ItemForm';
import ItemCard from '../components/ItemCard';

const Dashboard = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    priority: ''
  });

  useEffect(() => {
    fetchItems();
  }, [filters]);

  const fetchItems = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      Object.keys(filters).forEach(key => {
        if (filters[key]) params.append(key, filters[key]);
      });
      
      const res = await api.get(`/items?${params}`);
      setItems(res.data.items);
    } catch (error) {
      toast.error('Failed to fetch items');
    }
    setLoading(false);
  };

  const handleCreateItem = async (itemData) => {
    try {
      const res = await api.post('/items', itemData);
      setItems([res.data, ...items]);
      setShowForm(false);
      toast.success('Item created successfully!');
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to create item');
    }
  };

  const handleUpdateItem = async (itemData) => {
    try {
      const res = await api.put(`/items/${editingItem._id}`, itemData);
      setItems(items.map(item => 
        item._id === editingItem._id ? res.data : item
      ));
      setEditingItem(null);
      setShowForm(false);
      toast.success('Item updated successfully!');
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to update item');
    }
  };

  const handleDeleteItem = async (itemId) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await api.delete(`/items/${itemId}`);
        setItems(items.filter(item => item._id !== itemId));
        toast.success('Item deleted successfully!');
      } catch (error) {
        toast.error('Failed to delete item');
      }
    }
  };

  const handleEditItem = (item) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleFilterChange = (e) => {
    setFilters({
      ...filters,
      [e.target.name]: e.target.value
    });
  };

  const resetForm = () => {
    setShowForm(false);
    setEditingItem(null);
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="container">
      <div style={{ padding: '2rem 0' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
          <h1>Dashboard</h1>
          <button 
            onClick={() => setShowForm(true)} 
            className="btn btn-primary"
          >
            Add New Item
          </button>
        </div>

        {/* Filters */}
        <div className="card mb-2">
          <div className="card-header">Filters</div>
          <div className="card-body">
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <div className="form-group">
                <label>Category</label>
                <select 
                  name="category" 
                  value={filters.category} 
                  onChange={handleFilterChange}
                  className="form-control"
                >
                  <option value="">All Categories</option>
                  <option value="technology">Technology</option>
                  <option value="business">Business</option>
                  <option value="health">Health</option>
                  <option value="education">Education</option>
                  <option value="entertainment">Entertainment</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div className="form-group">
                <label>Status</label>
                <select 
                  name="status" 
                  value={filters.status} 
                  onChange={handleFilterChange}
                  className="form-control"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
              <div className="form-group">
                <label>Priority</label>
                <select 
                  name="priority" 
                  value={filters.priority} 
                  onChange={handleFilterChange}
                  className="form-control"
                >
                  <option value="">All Priorities</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Item Form Modal */}
        {showForm && (
          <div style={{ 
            position: 'fixed', 
            top: 0, 
            left: 0, 
            right: 0, 
            bottom: 0, 
            backgroundColor: 'rgba(0,0,0,0.5)', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{ 
              backgroundColor: 'white', 
              padding: '2rem', 
              borderRadius: '8px', 
              width: '90%', 
              maxWidth: '500px',
              maxHeight: '90vh',
              overflow: 'auto'
            }}>
              <ItemForm
                item={editingItem}
                onSubmit={editingItem ? handleUpdateItem : handleCreateItem}
                onCancel={resetForm}
              />
            </div>
          </div>
        )}

        {/* Items Grid */}
        {items.length === 0 ? (
          <div className="text-center p-2">
            <h3>No items found</h3>
            <p>Create your first item to get started!</p>
          </div>
        ) : (
          <div className="grid grid-2">
            {items.map(item => (
              <ItemCard
                key={item._id}
                item={item}
                onEdit={handleEditItem}
                onDelete={handleDeleteItem}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
